

import time
import tkinter as tk
from tkinter import messagebox, scrolledtext
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from PIL import Image, ImageTk
import io
import base64
import json

class BinanceLoginApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Binance 二维码登录")
        self.root.geometry("700x600")  # 增加窗口大小以显示更多信息

        self.label = tk.Label(root, text="等待二维码加载...")
        self.label.pack(pady=10)

        self.qr_label = tk.Label(root)
        self.qr_label.pack(pady=10)

        self.status_label = tk.Label(root, text="请扫描二维码登录")
        self.status_label.pack(pady=10)

        # 使用滚动文本框代替普通文本框，以便显示更多信息
        self.result_text = scrolledtext.ScrolledText(root, height=15, width=60)
        self.result_text.pack(pady=10, fill=tk.BOTH, expand=True)

        # 存储网络请求数据
        self.requests_data = []

        # 添加一个导出所有网络请求的按钮
        self.export_button = tk.Button(root, text="导出所有网络请求", command=self.export_all_requests)
        self.export_button.pack(pady=5)

        # 添加专门保存Cookie的按钮
        self.save_cookies_button = tk.Button(root, text="保存Cookies", command=self.save_cookies)
        self.save_cookies_button.pack(pady=5)

        # 启动浏览器
        self.start_browser()

    def start_browser(self):
        try:
            # 配置Chrome选项
            chrome_options = Options()
            # 添加一些选项使浏览器更稳定
            # chrome_options.add_argument("--disable-gpu")
            # chrome_options.add_argument("--no-sandbox")
            # chrome_options.add_argument("--disable-dev-shm-usage")
            # 如果需要无头模式，可以取消下面这行的注释
            # chrome_options.add_argument("--headless")

            # 启用更详细的网络日志
            chrome_options.set_capability("goog:loggingPrefs", {
                "performance": "ALL",
                "browser": "ALL",
                "network": "ALL"
            })

            # 添加扩展来捕获请求头 (可选)
            # 注意: 这需要事先下载相应的扩展文件
            # chrome_options.add_extension('path_to_request_capture_extension.crx')

            # 初始化浏览器 - 使用更稳定的方式
            try:
                # 先尝试直接初始化，不使用ChromeDriverManager
                self.driver = webdriver.Chrome(options=chrome_options)
            except Exception as e:
                # 如果失败，则使用ChromeDriverManager
                self.driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)

            # 启用Chrome开发者工具协议 (CDP)
            self.driver.execute_cdp_cmd("Network.enable", {})
            self.driver.execute_cdp_cmd("Network.setCacheDisabled", {"cacheDisabled": True})

            # 初始化请求数据列表
            self.requests_data = []

            # 打开Binance登录页面
            self.driver.get("https://accounts.binance.com/zh-CN/login?loginChannel=&return_to=")

            # 等待页面加载完成
            self.root.after(2000, self.click_qr_code_option)
        except Exception as e:
            messagebox.showerror("错误", f"启动浏览器时出错: {str(e)}")
            self.root.destroy()

    def click_qr_code_option(self):
        try:
            # 等待并点击二维码登录选项
            qr_option = WebDriverWait(self.driver, 20).until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="wrap_app"]/main/div/div[1]/div[2]/div[2]'))
            )
            qr_option.click()

            # 等待二维码出现
            self.root.after(2000, self.capture_qr_code)
        except Exception as e:
            self.status_label.config(text=f"点击二维码选项时出错: {str(e)}")

    def capture_qr_code(self):
        try:
            # 等待二维码对话框出现
            qr_modal = WebDriverWait(self.driver, 20).until(
                EC.visibility_of_element_located((By.CSS_SELECTOR, 'div.bn-bubble.bn-bubble__normal.bn-tooltips.active'))
            )

            # 截取二维码图像 - 先找到包含二维码的整个容器以获得更好的截图
            qr_container = WebDriverWait(self.driver, 10).until(
                EC.visibility_of_element_located((By.CSS_SELECTOR, 'div.bn-bubble.bn-bubble__normal.bn-tooltips.active .bn-bubble-content .flex.items-center.justify-center'))
            )
            qr_screenshot = qr_container.screenshot_as_png

            # 显示二维码图像在GUI中
            img = Image.open(io.BytesIO(qr_screenshot))
            img = img.resize((250, 250), Image.LANCZOS)
            qr_img = ImageTk.PhotoImage(img)

            self.qr_label.config(image=qr_img)
            self.qr_label.image = qr_img  # 保持引用

            self.label.config(text="请使用Binance App扫描二维码登录")

            # 开始检查登录状态
            self.check_login_status()
        except Exception as e:
            self.status_label.config(text=f"获取二维码时出错: {str(e)}")

    def check_login_status(self):
        try:
            # 检查是否已经登录成功
            # 通常可以通过检查页面URL变化或特定元素是否出现来判断
            current_url = self.driver.current_url

            # 检查是否在"保持登录"页面
            if "stay-signed-in" in current_url:
                self.status_label.config(text="检测到保持登录页面，正在处理...")
                self.result_text.insert(tk.END, "检测到保持登录页面，正在处理...\n")
                self.root.update()

                try:
                    # 点击保持登录复选框
                    stay_signed_checkbox = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, '//*[@id="stay-signed-checkbox"]/div'))
                    )
                    stay_signed_checkbox.click()
                    self.result_text.insert(tk.END, "已点击保持登录复选框\n")

                    # 点击确认按钮
                    confirm_button = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, '//*[@id="wrap_app"]/main/div/div/button[1]'))
                    )
                    confirm_button.click()
                    self.result_text.insert(tk.END, "已点击确认按钮\n")

                    # 继续检查登录状态
                    self.root.after(2000, self.check_login_status)
                except Exception as e:
                    self.result_text.insert(tk.END, f"处理保持登录页面时出错: {str(e)}\n")
                    # 即使出错也继续检查
                    self.root.after(2000, self.check_login_status)
            elif "https://www.binance.com" in current_url and "/login" not in current_url:
                # 登录成功
                self.status_label.config(text="登录成功！正在获取API请求头...")
                self.result_text.insert(tk.END, "登录成功！正在获取API请求头...\n")
                self.root.after(1000, self.navigate_to_target_page)
            else:
                # 继续等待登录
                self.root.after(2000, self.check_login_status)
        except Exception as e:
            self.status_label.config(text=f"检查登录状态时出错: {str(e)}")

    def navigate_to_target_page(self):
        try:
            # 导航到目标页面
            self.driver.get("https://www.binance.com/zh-CN/futures-activity/leaderboard/user?encryptedUid=13A05CEEA6B2B444C1AB8973255BAF9C")
            self.status_label.config(text="已访问目标页面，正在捕获API请求...")
            self.result_text.insert(tk.END, "已访问目标页面，等待API请求...\n")
            self.root.update()

            # 等待页面加载并捕获网络请求 - 增加等待时间确保API请求发出
            self.root.after(8000, self.capture_api_request)
        except Exception as e:
            self.status_label.config(text=f"访问目标页面时出错: {str(e)}")
            self.result_text.insert(tk.END, f"访问目标页面时出错: {str(e)}\n")

    def capture_api_request(self):
        try:
            # 使用更完整的方法获取网络请求
            all_network_logs = []

            # 1. 从性能日志获取基本信息
            performance_logs = self.driver.get_log("performance")
            for log in performance_logs:
                try:
                    if isinstance(log.get("message"), str):
                        log_message = json.loads(log.get("message"))
                    else:
                        log_message = log.get("message", {})

                    message = log_message.get("message", {})

                    # 收集请求信息
                    if message.get("method") == "Network.requestWillBeSent":
                        params = message.get("params", {})
                        all_network_logs.append(params)
                except Exception as e:
                    continue

            # 2. 获取当前cookies (这些将包含在请求中)
            cookies = self.driver.get_cookies()
            cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}
            cookie_str = '; '.join([f"{name}={value}" for name, value in cookie_dict.items()])

            # 添加更清晰的状态更新
            self.status_label.config(text="正在分析网络请求...")
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "正在搜索getOtherPosition请求...\n")
            self.root.update()

            # 过滤出getOtherPosition API请求
            found_requests = []

            for req_data in all_network_logs:
                try:
                    request = req_data.get("request", {})
                    request_url = request.get("url", "")

                    if "getOtherPosition" in request_url:
                        # 获取原始请求头
                        request_headers = request.get("headers", {})

                        # 如果请求头中没有Cookie，添加当前cookie
                        if "Cookie" not in request_headers and cookie_str:
                            request_headers["Cookie"] = cookie_str

                        # 添加到找到的请求列表
                        found_requests.append({
                            "url": request_url,
                            "headers": request_headers,
                            "method": request.get("method", ""),
                            "timestamp": req_data.get("timestamp", ""),
                            "requestId": req_data.get("requestId", ""),
                            "cookies": cookie_dict  # 单独保存cookie以便查看
                        })
                except Exception as e:
                    continue

            # 存储找到的请求以便后续处理
            if found_requests:
                self.requests_data = found_requests

            if found_requests:
                # 显示找到的所有请求信息
                self.result_text.delete(1.0, tk.END)
                self.result_text.insert(tk.END, f"找到 {len(found_requests)} 个getOtherPosition请求\n\n")

                for i, req in enumerate(found_requests):
                    self.result_text.insert(tk.END, f"请求 #{i+1}:\n")
                    self.result_text.insert(tk.END, f"URL: {req['url']}\n")
                    self.result_text.insert(tk.END, f"方法: {req['method']}\n")
                    self.result_text.insert(tk.END, "请求头:\n")

                    # 显示所有请求头信息，包括cookie
                    for key, value in req['headers'].items():
                        self.result_text.insert(tk.END, f"{key}: {value}\n")

                    # 保存完整请求头（包含Cookie）到文件
                    self.save_headers_to_file(req['headers'], f"binance_headers_{i+1}.txt")
                    self.result_text.insert(tk.END, f"\n请求头已保存到文件: binance_headers_{i+1}.txt\n\n")

                self.status_label.config(text="成功获取API请求头信息")
            else:
                # 如果没有找到请求，尝试再等待一会
                self.status_label.config(text="未找到getOtherPosition API请求，正在重试...")
                self.result_text.insert(tk.END, "未找到请求，将在3秒后重试...\n")
                # 显示当前所有Cookie以供参考
                self.result_text.insert(tk.END, "\n当前Cookies:\n")
                for cookie in cookies:
                    self.result_text.insert(tk.END, f"{cookie['name']}: {cookie['value']}\n")

                self.root.after(3000, self.capture_api_request)
        except Exception as e:
            self.status_label.config(text=f"获取API请求头时出错: {str(e)}")
            self.result_text.insert(tk.END, f"错误: {str(e)}\n")


    def save_cookies(self):
        """保存当前浏览器中的所有Cookie到文件"""
        try:
            # 获取当前所有Cookie
            cookies = self.driver.get_cookies()

            # 保存为JSON文件
            with open('binance_cookies.json', 'w', encoding='utf-8') as f:
                json.dump(cookies, f, ensure_ascii=False, indent=2)

            # 构建Cookie字符串（如请求头中的格式）
            cookie_str = '; '.join([f"{cookie['name']}={cookie['value']}" for cookie in cookies])

            # 保存为Cookie格式的文本文件
            with open('binance_cookies.txt', 'w', encoding='utf-8') as f:
                f.write(cookie_str)

            self.result_text.insert(tk.END, "\nCookies已保存到 binance_cookies.json 和 binance_cookies.txt\n")
            self.result_text.insert(tk.END, f"Cookie字符串:\n{cookie_str}\n")

            messagebox.showinfo("保存成功", "Cookie已保存到文件")
        except Exception as e:
            self.result_text.insert(tk.END, f"\n保存Cookie时出错: {str(e)}\n")
            messagebox.showerror("保存失败", f"保存Cookie时出错: {str(e)}")


    def export_all_requests(self):
        """导出所有网络请求到文件"""
        try:
            # 获取性能日志
            logs = self.driver.get_log("performance")
            all_requests = []

            for log in logs:
                try:
                    log_message = log.get("message", {})
                    if not isinstance(log_message, dict):
                        log_message = json.loads(log_message)

                    message = log_message.get("message", {})
                    if message.get("method") == "Network.requestWillBeSent":
                        request_data = message.get("params", {})

                        # 获取当前cookies
                        cookies = self.driver.get_cookies()
                        cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}
                        cookie_str = '; '.join([f"{name}={value}" for name, value in cookie_dict.items()])

                        # 如果请求数据中没有Cookie，添加当前cookie
                        request_headers = request_data.get("request", {}).get("headers", {})
                        if "Cookie" not in request_headers and cookie_str:
                            request_headers["Cookie"] = cookie_str

                        # 添加请求数据
                        all_requests.append(request_data)
                except:
                    continue

            # 保存所有请求到JSON文件
            with open("all_binance_requests.json", "w", encoding="utf-8") as f:
                json.dump(all_requests, f, ensure_ascii=False, indent=2)

            # 特别过滤getOtherPosition请求
            getOtherPosition_requests = []
            for req in all_requests:
                try:
                    request_url = req.get("request", {}).get("url", "")
                    if "getOtherPosition" in request_url:
                        getOtherPosition_requests.append(req)
                except:
                    continue

            # 保存getOtherPosition请求到特定JSON文件
            with open("getOtherPosition_requests.json", "w", encoding="utf-8") as f:
                json.dump(getOtherPosition_requests, f, ensure_ascii=False, indent=2)

            # 保存当前的Cookies
            self.save_cookies()

            self.result_text.insert(tk.END, "\n所有网络请求已导出到 all_binance_requests.json\n")
            self.result_text.insert(tk.END, "getOtherPosition请求已导出到 getOtherPosition_requests.json\n")

            # 尝试提取最完整的请求头作为参考
            if getOtherPosition_requests:
                most_complete_headers = {}
                for req in getOtherPosition_requests:
                    headers = req.get("request", {}).get("headers", {})
                    if len(headers) > len(most_complete_headers):
                        most_complete_headers = headers

                # 保存最完整的请求头到特别的文件
                with open("most_complete_headers.txt", "w", encoding="utf-8") as f:
                    for key, value in most_complete_headers.items():
                        f.write(f"{key}: {value}\n")

                self.result_text.insert(tk.END, "最完整的请求头已保存到 most_complete_headers.txt\n")

            messagebox.showinfo("导出成功", "所有网络请求和Cookies已导出到文件")
        except Exception as e:
            self.result_text.insert(tk.END, f"\n导出请求时出错: {str(e)}\n")
            messagebox.showerror("导出错误", f"导出网络请求时发生错误: {str(e)}")

    def save_headers_to_file(self, headers, filename):
        """将请求头保存到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                for key, value in headers.items():
                    f.write(f"{key}: {value}\n")

            # 尝试更新looknew.py文件
            self.update_looknew_file(headers)
        except Exception as e:
            self.result_text.insert(tk.END, f"保存文件时出错: {str(e)}\n")

    def update_looknew_file(self, headers):
        """更新looknew.py文件中的cookie、csrftoken和fvideo-id"""
        try:
            # 获取需要的头信息
            cookie = headers.get("Cookie", "") or headers.get("cookie", "")
            csrftoken = ""
            fvideo_id = ""

            # 从头信息中提取csrftoken
            for key, value in headers.items():
                if key.lower() == "csrftoken":
                    csrftoken = value
                elif key.lower() == "fvideo-id":
                    fvideo_id = value

            # 如果没有直接找到csrftoken，尝试从cookie中提取
            if not csrftoken and cookie:
                cookie_parts = cookie.split(';')
                for part in cookie_parts:
                    if 'csrftoken=' in part.lower():
                        csrftoken = part.split('=', 1)[1].strip()

            # 如果没有直接找到fvideo-id，尝试从cookie中提取BNC_FV_KEY
            if not fvideo_id and cookie:
                cookie_parts = cookie.split(';')
                for part in cookie_parts:
                    if 'bnc_fv_key=' in part.lower():
                        fvideo_id = part.split('=', 1)[1].strip()

            # 如果成功获取了至少一个值，就更新looknew.py文件
            if cookie or csrftoken or fvideo_id:
                self.result_text.insert(tk.END, "正在更新looknew.py文件...\n")

                # 读取looknew.py文件
                with open('looknew.py', 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                # 更新相应行
                if cookie and 63 < len(lines):
                    # 更新第64行cookie
                    lines[63] = f'    "cookie": "{cookie}",\n'

                if csrftoken and 64 < len(lines):
                    # 更新第65行csrftoken
                    lines[64] = f'    "csrftoken": "{csrftoken}",\n'

                if fvideo_id and 66 < len(lines):
                    # 更新第67行fvideo-id
                    lines[66] = f'    "fvideo-id": "{fvideo_id}",\n'

                # 写回文件
                with open('looknew.py', 'w', encoding='utf-8') as f:
                    f.writelines(lines)

                self.result_text.insert(tk.END, "成功更新looknew.py文件\n")
                if cookie:
                    self.result_text.insert(tk.END, "更新了cookie\n")
                if csrftoken:
                    self.result_text.insert(tk.END, "更新了csrftoken\n")
                if fvideo_id:
                    self.result_text.insert(tk.END, "更新了fvideo-id\n")

                # 添加运行looknew.py的按钮
                self.run_looknew_button = tk.Button(self.root, text="运行looknew.py脚本", command=self.run_looknew_script, bg="green", fg="white")
                self.run_looknew_button.pack(pady=10)

                # 自动运行looknew.py脚本
                self.result_text.insert(tk.END, "正在自动运行looknew.py脚本...\n")
                self.root.update()
                self.run_looknew_script()
            else:
                self.result_text.insert(tk.END, "未找到需要的头信息，无法更新looknew.py\n")
        except Exception as e:
            self.result_text.insert(tk.END, f"更新looknew.py文件时出错: {str(e)}\n")

    def run_looknew_script(self):
        """运行looknew.py脚本并关闭当前程序"""
        try:
            import subprocess
            import os
            import time

            # 使用您指定的完整路径
            script_path = "E:\\project_Py\\BiAn\\looknew.py"
            self.result_text.insert(tk.END, f"脚本路径: {script_path}\n")

            # 使用subprocess模块打开cmd并运行脚本
            cmd_command = f"start cmd /k python {script_path}"
            self.result_text.insert(tk.END, f"执行命令: {cmd_command}\n")

            # 执行命令
            process = subprocess.Popen(cmd_command, shell=True)

            self.result_text.insert(tk.END, "已成功启动looknew.py脚本\n")
            self.result_text.insert(tk.END, "将在 3 秒后关闭浏览器和当前程序...\n")
            self.root.update()

            # 等待几秒，确保looknew.py脚本已经启动
            time.sleep(3)

            # 关闭浏览器
            if hasattr(self, 'driver'):
                self.driver.quit()

            # 关闭当前程序
            self.root.quit()
            self.root.destroy()
        except Exception as e:
            self.result_text.insert(tk.END, f"运行looknew.py脚本时出错: {str(e)}\n")
            messagebox.showerror("运行错误", f"运行looknew.py脚本时出错: {str(e)}")

    def __del__(self):
        # 关闭浏览器
        try:
            if hasattr(self, 'driver'):
                self.driver.quit()
        except:
            pass


if __name__ == "__main__":
    root = tk.Tk()
    app = BinanceLoginApp(root)
    root.mainloop()