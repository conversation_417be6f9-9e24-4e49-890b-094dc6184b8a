import requests
import json
import os
import smtplib
import time
import logging
from datetime import datetime
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import csv

# 配置日志
def setup_logger():
    # 创建日志目录
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)

    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    # 创建正常运行日志文件处理器
    info_file_handler = logging.FileHandler(os.path.join(log_dir, 'info.log'), encoding='utf-8')
    info_file_handler.setLevel(logging.INFO)
    info_file_handler.setFormatter(formatter)
    root_logger.addHandler(info_file_handler)

    # 创建错误日志文件处理器
    error_file_handler = logging.FileHandler(os.path.join(log_dir, 'error.log'), encoding='utf-8')
    error_file_handler.setLevel(logging.ERROR)
    error_file_handler.setFormatter(formatter)
    root_logger.addHandler(error_file_handler)

    return root_logger

# 初始化日志记录器
logger = setup_logger()

# 登录网站
# https://accounts.binance.com/zh-CN/login

# 请求网站
# https://www.binance.com/zh-CN/futures-activity/leaderboard/user?encryptedUid=13A05CEEA6B2B444C1AB8973255BAF9C
# 设置请求的接口
url = "https://www.binance.com/bapi/futures/v2/private/future/leaderboard/getOtherPosition"

# 设置请求头
headers = {
    "accept": "*/*",
    "accept-language": "zh-CN,zh;q=0.9",
    "bnc-uuid": "a25ccae8-7d42-4e2c-b8e1-73aa2ac8508a",
    "clienttype": "web",
    "content-type": "application/json",
    "cookie": "theme=dark; _ga_3WP50LGEEC=GS2.1.s1749723002$o1$g1$t1749723026$j36$l0$h0; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%***********%22%2C%22first_id%22%3A%22197639e1ed4737-0e1b401f76e7a1-26011e51-2073600-197639e1ed5452%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk3NjM5ZTFlZDQ3MzctMGUxYjQwMWY3NmU3YTEtMjYwMTFlNTEtMjA3MzYwMC0xOTc2MzllMWVkNTQ1MiIsIiRpZGVudGl0eV9sb2dpbl9pZCI6IjE3NTk1OTA5NCJ9%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%***********%22%7D%2C%22%24device_id%22%3A%22197639e86d0a54-06f50b2ac5a2df-26011e51-2073600-197639e86d1ea8%22%7D; aws-waf-token=7dc21d5e-b317-4734-b32a-765cd995b9d9:BgoAsodHXzkGAAAA:nnRC9oaOZUqyqxTonk7zhxvypfeXNoghGGUVc8SOfx6BeLC1mfDpVG61Nu01s9tGJA9oCdrPFKDDjC2U9cHM5y1tu2hSRPNQ+knPYILlZIuKVyEvsPNs7A/Kou9t7GcLQ3rmDH/jpgs99VSh/bkkJQ3xvIEIh0RygtOHW1iILYRaexdpOrIslfB3jFm4nw+Z63gTSLyhwTI9gS6j4HZ/oA==; p20t=web.*********.55C2EA8D73A9EE0F7C043879F31D35E7; se_gd=lIVFwDgtSDQEwIb0XDFJgZZHRVAQVBQVFILVRW0FlhTUQDFNWVEe1; _gid=GA1.2.**********.**********; logined=y; sajssdk_2015_cross_new_user=1; currentAccount=; cr00=4E63528E512A9B91CB06F88E59D26D37; r20t=web.03B8A45A41DBC1B4DADCF609D30A2123; BNC_FV_KEY_T=101-M9zjqwgwh3Yf7wCsK%2BH8n2ZY6frq8y3qZilENNiQL3KhqdYpRCbq5aQmkvPHTWAUkeODFVZ0X%2FQD8MDzMV0pcg%3D%3D-aVgbINeM8a71icEEmcT0bQ%3D%3D-34; r30t=1; language=zh-CN; BNC_FV_KEY=332d88f6d52ea146b945973389ccebb9f7fc7ce7; f30l=web.*********.29370AFD86962AF144E91D626BEDAEDE; lang=zh-CN; se_gsd=AjonGjtVIDMkBhYwJBMnDiozUwAIBwcWWFtGUVZbV1hVI1NT1; _gat=1; OptanonAlertBoxClosed=2025-06-12T10:09:58.959Z; OptanonConsent=isGpcEnabled=0&datestamp=Thu+Jun+12+2025+18%3A10%3A23+GMT%2B0800+(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4)&version=202411.2.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=1d1f241d-fe61-43ed-a379-f9069e62b6d5&interactionCount=1&isAnonUser=1&landingPath=NotLandingPage&groups=C0001%3A1%2CC0003%3A1%2CC0004%3A1%2CC0002%3A1&intType=1&geolocation=HK%3B&AwaitingReconsent=false; se_sd=RkPEVWwRVGWElxaYSWlVgZZFhXAFQEQVFMVVRW0FlhTUQElNWVIL1; _gat_UA-*********-1=1; BNC_FV_KEY_EXPIRE=1749744604070; d1og=web.*********.483A8F030D6A2E087EDA8BBF2BF50B07; BNC-Location=CN; _ga=GA1.1.285078103.**********; r2o1=web.*********.73488830839B6FCD826062640503972E; bnc-uuid=bcba669f-2bf2-423b-a92b-19b3063554ab",
    "csrftoken": "bc19d10e8306670ba058e7d429c9e6e8",
    "device-info": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "fvideo-id": "332d88f6d52ea146b945973389ccebb9f7fc7ce7",
    "fvideo-token": "c6i0XkSyLW5QLqraPA6BxXdcGII3J7gtKUKZNHiyunnlaij+gCKXe/k0v/Wg/HJ//rkFzmCQhy3NBgNbfD5GB7b5p+dEA2A8Jzi1SM/SY1SXCnrXYfF5M3LRmnsJltNCQEfEq/4TVcxlWNtEUyEYD4vNGM9LiHmhTVndx/7pVItnRMEzWEc5MClNO1x/gI8G0=64",
    "lang": "zh-CN",
    "origin": "https://www.binance.com",
    "priority": "u=1, i",
    "referer": "https://www.binance.com/zh-CN/futures-activity/leaderboard/user?encryptedUid=13A05CEEA6B2B444C1AB8973255BAF9C",
    "sec-ch-ua": '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"',
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "x-passthrough-token": "",
    "x-trace-id": "41e124e1-79a8-4ae7-a37c-bcfda8a1aa15",
    "x-ui-request-trace": "41e124e1-79a8-4ae7-a37c-bcfda8a1aa15"
}

# 设置请求数据
data = {
    "encryptedUid": "13A05CEEA6B2B444C1AB8973255BAF9C",
    "tradeType": "PERPETUAL"
}

# 全局数据存储
position_data = {}
last_position_data = {}
data_file = "position_data.json"

# 邮件配置
email_enabled = True
email_sender = "<EMAIL>"
email_password = "dlesakfqioexhgeh"
email_recipient = "<EMAIL>"
email_smtp_server = "smtp.qq.com"
email_smtp_port = 587

# 监控配置
refresh_interval = 3  # 刷新间隔(秒)
position_change_threshold = 5  # 仓位变化阈值(百分比)
alert_threshold = -25  # 提醒阈值
alert_status_file = "alert_status.json"  # 提醒状态存储文件
alert_status = {}  # 记录提醒状态 {symbol: 是否已提醒}
# 新增日志记录相关
log_file = "position_log.csv"
position_history = {}  # 存储仓位历史数据 {symbol: {开仓数据, 补仓记录}}

# 移除OKX配置

# ============================= 配置信息 ==============================#
# 在文件开头添加新的常量定义
COLUMNS = [
    "交易对", "开仓时间", "开仓价格", "开仓数量", "杠杆", "开仓金额",
    "补仓记录", "总补仓次数", "总补仓数量", "总变动金额",
    "平仓状态", "平仓数量", "平仓时间", "最终持仓价值",
    "累计收益率(%)", "累计收益金额"
]

# 添加新的临时记录文件
TEMP_RECORD_FILE = "temp_position_record.txt"

def init_csv_file():
    """初始化CSV文件，如果不存在则创建并写入表头"""
    if not os.path.exists(log_file):
        with open(log_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(COLUMNS)


# 移除OKX相关函数
def save_temp_record(symbol, action_type):
    """保存临时记录"""
    try:
        with open(TEMP_RECORD_FILE, 'w', encoding='utf-8') as f:
            f.write(f"{symbol},{action_type},{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    except Exception as e:
        logger.error(f"保存临时记录失败: {e}")


def load_temp_record():
    """加载临时记录"""
    try:
        if os.path.exists(TEMP_RECORD_FILE):
            with open(TEMP_RECORD_FILE, 'r', encoding='utf-8') as f:
                content = f.read().strip().split(',')
                if len(content) >= 2:
                    return content[0], content[1]
    except Exception as e:
        logger.error(f"加载临时记录失败: {e}")
    return None, None

def clear_temp_record():
    """清除临时记录"""
    try:
        if os.path.exists(TEMP_RECORD_FILE):
            os.remove(TEMP_RECORD_FILE)
    except Exception as e:
        logger.error(f"清除临时记录失败: {e}")


def send_email(subject, message):
    """发送邮件通知（移植自old.py）"""
    if not email_enabled:
        logger.info("邮件通知已禁用")
        return

    try:
        msg = MIMEMultipart()
        msg['From'] = email_sender
        msg['To'] = email_recipient
        msg['Subject'] = subject
        msg.attach(MIMEText(message, 'html'))

        server = smtplib.SMTP(email_smtp_server, email_smtp_port)
        server.starttls()
        server.login(email_sender, email_password)
        server.send_message(msg)
        server.quit()
        logger.info(f"邮件已发送: {subject}")
    except Exception as e:
        logger.error(f"发送邮件失败: {e}")


def check_position_changes(symbol, current_data):
    """检查交易对变化（移植自old.py）"""
    if symbol not in last_position_data:
        update_position_log(symbol, "open", current_data)  # 记录开仓
        # 新增交易对通知，包含开仓使用金额
        opening_amount = current_data["开仓金额"]
        leverage = current_data["杠杆"]
        position_size = current_data["数量"]
        # 获取仓位类型
        position_type = current_data.get("仓位类型", "多仓")

        # 计算建议开仓数量（原始数量除以2000）
        original_qty = float(current_data.get("原始数量", current_data["数量"]))
        suggested_qty = abs(original_qty) / 2000
        direction = "做空" if position_type == "空仓" else "做多"

        # 移除OKX开仓操作

        send_email(
            f"新增交易对 {symbol} ({position_type})",
            f"""<h2>新增交易对 {symbol} ({position_type})</h2>
            <p>时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p><strong>开仓使用金额: {opening_amount} USDT</strong></p>
            <p>杠杆: {leverage}x | 持仓数量: {position_size}</p>

            <h3>开单建议（资金缩放后）:</h3>
            <ul>
                <li>方向: {direction}</li>
                <li>杠杆: {leverage}x</li>
                <li>建议数量: {suggested_qty:.6f}</li>
            </ul>

            <table border="1">
                <tr><th>指标</th><th>值</th></tr>
                {"".join(f"<tr><td>{k}</td><td>{v}</td></tr>" for k, v in current_data.items())}
            </table>"""
        )
        return

    # 检查仓位变化
    prev_data = last_position_data[symbol]
    changes = []
    try:
        # 使用原始数量进行计算
        prev_qty = float(prev_data.get("原始数量", prev_data["数量"]))
        curr_qty = float(current_data.get("原始数量", current_data["数量"]))

        # 计算金额变化
        curr_price = float(current_data["标记价格"])
        leverage = float(current_data["杠杆"])

        # 计算变化金额 = (当前数量 - 之前数量) * 当前价格 / 杠杆
        qty_change = curr_qty - prev_qty
        amount_change = abs(qty_change * curr_price / leverage)
        change_pct = abs(qty_change / prev_qty) * 100 if prev_qty != 0 else 0

        # 判断仓位类型
        position_type = current_data.get("仓位类型", "多仓" if curr_qty > 0 else "空仓")

        if change_pct > position_change_threshold:
            update_position_log(symbol, "add", current_data)  # 记录补仓

            # 根据仓位类型和数量变化判断加减仓
            is_adding = False
            if position_type == "多仓":
                is_adding = curr_qty > prev_qty
                change_type = "加仓" if is_adding else "减仓"
            else:  # 空仓
                is_adding = abs(curr_qty) > abs(prev_qty)
                change_type = "加仓" if is_adding else "减仓"

            # 显示绝对值
            changes.append(f"仓位变化 {change_type}: {abs(prev_qty):.3f} → {abs(curr_qty):.3f} ({change_pct:.3f}%)")
            changes.append(f"金额变化: {amount_change:.3f} USDT")
            changes.append(f"仓位类型: {position_type}")

            # 移除OKX加减仓操作
    except Exception as e:
        logger.error(f"计算仓位变化出错: {e}")

    if changes:
        # 获取仓位类型
        position_type = current_data.get("仓位类型", "多仓")

        # 计算建议开仓数量（原始数量除以2000）
        original_qty = float(current_data.get("原始数量", current_data["数量"]))
        suggested_qty = abs(original_qty) / 2000
        direction = "做空" if position_type == "空仓" else "做多"

        send_email(
            f"交易对 {symbol} ({position_type}) 变化提醒",
            f"""<h2>{symbol} ({position_type}) 仓位变化</h2>
            <p>时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <ul>{"".join(f"<li>{c}</li>" for c in changes)}</ul>

            <h3>当前开单建议（资金缩放后）:</h3>
            <ul>
                <li>方向: {direction}</li>
                <li>杠杆: {current_data["杠杆"]}x</li>
                <li>建议数量: {suggested_qty:.6f}</li>
            </ul>

            <h3>当前数据:</h3>
            <table border="1">
                <tr><th>指标</th><th>值</th></tr>
                {"".join(f"<tr><td>{k}</td><td>{v}</td></tr>" for k, v in current_data.items())}
            </table>"""
        )


def load_alert_status():
    """加载提醒状态"""
    global alert_status
    if os.path.exists(alert_status_file):
        try:
            with open(alert_status_file, 'r', encoding='utf-8') as f:
                alert_status = json.load(f)
            logger.info(f"已加载提醒状态: {len(alert_status)}个交易对")
        except Exception as e:
            logger.error(f"加载提醒状态失败: {e}")
            alert_status = {}


def save_alert_status():
    """保存提醒状态"""
    try:
        with open(alert_status_file, 'w', encoding='utf-8') as f:
            json.dump(alert_status, f, ensure_ascii=False, indent=2)
        logger.info(f"提醒状态已保存到 {alert_status_file}")
    except Exception as e:
        logger.error(f"保存提醒状态失败: {e}")


def check_roe_alert(symbol, current_roe):
    """检查收益率阈值提醒"""
    global alert_status

    # 初始化状态（针对新交易对）
    if symbol not in alert_status:
        alert_status[symbol] = False

    current_status = alert_status[symbol]
    should_alert = False

    try:
        current_roe = float(current_roe)
        # 当前收益率低于阈值且之前未提醒
        if current_roe < alert_threshold and not current_status:
            should_alert = True
            alert_status[symbol] = True  # 标记为已提醒
        # 当前收益率回升到阈值以上且之前已提醒
        elif current_roe >= alert_threshold and current_status:
            alert_status[symbol] = False  # 重置状态

    except ValueError:
        print(f"无效的收益率数据: {current_roe}", flush=True)
        return

    if should_alert and symbol in position_data:
        # 获取交易相关数据
        position_info = position_data[symbol]
        leverage = position_info["杠杆"]
        position_size = position_info["数量"]
        opening_amount = position_info["开仓金额"] if "开仓金额" in position_info else "N/A"
        position_value = position_info["持仓价值"] if "持仓价值" in position_info else "N/A"

        # 获取仓位类型
        position_type = position_info.get("仓位类型", "多仓")

        # 计算建议开仓数量（原始数量除以2000）
        original_qty = float(position_info.get("原始数量", position_info["数量"]))
        suggested_qty = abs(original_qty) / 2000
        direction = "做空" if position_type == "空仓" else "做多"

        send_email(
            f"【加仓提醒】{symbol} ({position_type}) 收益率低于{alert_threshold}%",
            f"""<h2>{symbol} ({position_type}) 达到加仓提醒阈值</h2>
            <p>时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>当前收益率: {current_roe:.3f}%</p>
            <p><strong>交易信息:</strong></p>
            <ul>
                <li>仓位类型: {position_type}</li>
                <li>杠杆: {leverage}x</li>
                <li>持仓数量: {position_size}</li>
                <li>开仓金额: {opening_amount} USDT</li>
                <li>当前持仓价值: {position_value} USDT</li>
            </ul>

            <h3>当前开单建议（资金缩放后）:</h3>
            <ul>
                <li>方向: {direction}</li>
                <li>杠杆: {leverage}x</li>
                <li>建议数量: {suggested_qty:.6f}</li>
            </ul>

            <p>该交易对收益率已首次跌破{alert_threshold}%，建议关注加仓机会</p>"""
        )
        save_alert_status()


def load_position_data():
    """加载历史数据（修改自old.py）"""
    global position_data, last_position_data
    if os.path.exists(data_file):
        try:
            with open(data_file, 'r', encoding='utf-8') as f:
                position_data = json.load(f)
                last_position_data = position_data.copy()
                logger.info(f"已加载历史数据: {len(position_data)}个交易对")
        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            position_data = {}
            last_position_data = {}


def save_position_data():
    """保存当前数据（修改自old.py）"""
    try:
        # 创建一个临时数据副本，用于格式化数值
        formatted_data = {}
        for symbol, data in position_data.items():
            formatted_data[symbol] = {}
            for key, value in data.items():
                # 对浮点数值保留3位小数
                if isinstance(value, float) or (isinstance(value, str) and value.replace('.', '', 1).replace('-', '', 1).isdigit()):
                    try:
                        formatted_value = float(value)
                        formatted_data[symbol][key] = f"{formatted_value:.3f}"
                    except (ValueError, TypeError):
                        formatted_data[symbol][key] = value
                else:
                    formatted_data[symbol][key] = value

        with open(data_file, 'w', encoding='utf-8') as f:
            json.dump(formatted_data, f, ensure_ascii=False, indent=2)
        logger.info(f"数据已保存到 {data_file}")
    except Exception as e:
        logger.error(f"保存数据失败: {e}")


def fetch_positions():
    """获取仓位数据（增加重试机制）"""
    retries = 3
    timeout = 10  # 单次请求超时时间
    cookie_expired = False  # 标记cookie是否过期

    # 以下是原始的API请求代码
    for attempt in range(retries):
        try:
            response = requests.post(url, headers=headers, json=data, timeout=timeout)

            # HTTP状态码和业务状态码双重验证
            if response.status_code != 200:
                logger.warning(f"请求失败（尝试 {attempt + 1}/{retries}）HTTP状态码: {response.status_code}")
                continue

            response_data = response.json()
            if not response_data.get('success'):
                error_message = response_data.get('message', '')
                error_code = response_data.get('code', '')
                logger.warning(f"业务请求失败（尝试 {attempt + 1}/{retries}）: {error_message} (代码: {error_code})")

                # 检测是否是cookie过期错误
                if error_code == "100001005" or "Please log in first" in error_message:
                    cookie_expired = True
                    logger.error("检测到Cookie已过期，需要更新")
                    break  # 直接跳出重试循环
                continue

            positions = response_data['data']['otherPositionRetList']
            logger.info(f"获取到{len(positions)}个交易对")

            current_symbols = set()
            new_data = {}

            for position in positions:
                symbol = position['symbol']
                current_symbols.add(symbol)
                update_time = position['updateTime']

                # 增强时间解析容错
                try:
                    dt = datetime(*update_time[:6])
                except Exception as e:
                    logger.error(f"时间解析错误 {update_time}: {e}")
                    dt = datetime.now()

                # 计算交易金额
                leverage = float(position['leverage'])
                amount = float(position['amount'])
                entry_price = float(position['entryPrice'])
                mark_price = float(position['markPrice'])

                # 计算开仓使用金额 = 数量 * 开仓价格 / 杠杆
                opening_amount = abs(amount * entry_price / leverage)
                # 当前持仓价值 = 数量 * 标记价格
                position_value = abs(amount * mark_price)
                # 判断是多仓还是空仓
                position_type = "多仓" if amount > 0 else "空仓"

                position_info = {
                    "交易对": symbol,
                    "杠杆": f"{leverage:.3f}",
                    "数量": f"{abs(amount):.3f}",
                    "开仓价格": f"{entry_price:.3f}",
                    "标记价格": f"{mark_price:.3f}",
                    "时间": dt.strftime('%Y-%m-%d %H:%M:%S'),
                    "收益额": f"{float(position['pnl']):.3f}",
                    "收益率": f"{position['roe'] * 100:.3f}",
                    "开仓金额": f"{opening_amount:.3f}",  # 新增字段：开仓使用金额
                    "持仓价值": f"{position_value:.3f}",  # 新增字段：当前持仓价值
                    "最后更新时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "仓位类型": position_type,  # 新增字段：标记多空仓位
                    "原始数量": f"{amount:.3f}"  # 保存原始数量用于计算变化
                }
                new_data[symbol] = position_info

                # 输出日志
                logger.debug(f"--- 交易对 {symbol} ---")
                for k, v in position_info.items():
                    logger.debug(f"{k}: {v}")
                logger.debug("-" * 30)

            return new_data, current_symbols, False  # 返回第三个参数表示cookie未过期

        except requests.exceptions.RequestException as e:
            logger.error(f"网络异常（尝试 {attempt + 1}/{retries}）: {str(e)}")
            time.sleep(2)
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败（尝试 {attempt + 1}/{retries}）: {str(e)}")
            time.sleep(2)
        except KeyError as e:
            logger.error(f"数据字段缺失（尝试 {attempt + 1}/{retries}）: {str(e)}")
            time.sleep(2)

    # 如果cookie过期，返回特殊标记
    if cookie_expired:
        return None, set(), True  # 返回第三个参数表示cookie已过期

    logger.error("所有重试均失败，返回空数据")
    return None, set(), False


def update_position_log(symbol, action_type, data=None):
    """更新仓位日志"""
    try:
        # 保存临时记录
        save_temp_record(symbol, action_type)

        if symbol not in position_history:
            position_history[symbol] = {
                "open_time": None,
                "open_price": None,
                "open_qty": None,
                "leverage": None,
                "opening_amount": None,
                "add_records": [],
                "close_time": None,
                "close_qty": None,
                "position_value": None
            }

        record = position_history[symbol]

        if action_type == "open" and data:
            leverage = float(data["杠杆"]) if "杠杆" in data else 1.0
            opening_amount = float(data["开仓金额"]) if "开仓金额" in data else \
                (float(data["数量"]) * float(data["开仓价格"]) / leverage)

            record.update({
                "open_time": data["时间"],
                "open_price": float(data["开仓价格"]),
                "open_qty": float(data["数量"]),
                "leverage": leverage,
                "opening_amount": opening_amount
            })

        elif action_type == "add" and data:
            # 使用原始数量进行计算
            current_qty = float(data.get("原始数量", data["数量"]))
            previous_qty = record["open_qty"] + sum(a["qty"] for a in record["add_records"])
            change_qty = current_qty - previous_qty
            leverage = float(data["杠杆"]) if "杠杆" in data else record["leverage"] or 1.0
            price = float(data["标记价格"]) if "标记价格" in data else float(data["开仓价格"])
            change_amount = abs(change_qty * price / leverage)

            # 判断仓位类型
            position_type = data.get("仓位类型", "多仓" if current_qty > 0 else "空仓")

            # 对于空仓，判断绝对值是否增加
            is_adding = True
            if position_type == "空仓":
                is_adding = abs(current_qty) > abs(previous_qty)
            else:  # 多仓
                is_adding = current_qty > previous_qty

            if is_adding:
                record["add_records"].append({
                    "time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "qty": abs(change_qty),  # 保存绝对值
                    "amount": change_amount,
                    "position_type": position_type
                })

        elif action_type == "close":
            record.update({
                "close_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "close_qty": float(data["数量"]) if data and "数量" in data else 0,
                "position_value": data["持仓价值"] if data and "持仓价值" in data else None
            })

            # 准备CSV数据行
            add_records_str = "\n".join(
                [f"{a['time']}: +{a['qty']} (金额: {a.get('amount', 'N/A'):.3f} USDT)"
                 for a in record["add_records"]]
            ) or "无"

            total_add_qty = sum(a["qty"] for a in record["add_records"])
            total_add_amount = sum(a.get("amount", 0) for a in record["add_records"])

            csv_row = [
                symbol,                                         # 交易对
                record["open_time"],                           # 开仓时间
                record["open_price"],                          # 开仓价格
                record["open_qty"],                            # 开仓数量
                record["leverage"],                            # 杠杆
                record["opening_amount"],                      # 开仓金额
                add_records_str,                               # 补仓记录
                len(record["add_records"]),                    # 总补仓次数
                total_add_qty,                                 # 总补仓数量
                f"{total_add_amount:.3f}",                     # 总变动金额
                "已平仓" if record["close_time"] else "持仓中",  # 平仓状态
                record["close_qty"],                           # 平仓数量
                record["close_time"],                          # 平仓时间
                record["position_value"],                      # 最终持仓价值
                data["收益率"] if data and "收益率" in data else 0,  # 累计收益率(%)
                data["收益额"] if data and "收益额" in data else 0   # 累计收益金额
            ]

            # 写入CSV
            with open(log_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(csv_row)

            # 清理历史记录和临时文件
            del position_history[symbol]
            clear_temp_record()

    except Exception as e:
        logger.error(f"更新日志失败: {e}")


def monitor():
    """主监控循环"""
    init_csv_file()  # 确保CSV文件存在并有正确的表头

    # 检查是否有未完成的记录
    symbol, action_type = load_temp_record()
    if symbol and action_type:
        logger.info(f"发现未完成的记录: {symbol} - {action_type}")
        # 这里可以根据需要处理未完成的记录
        clear_temp_record()

    # 初始化监控循环
    load_position_data()
    load_alert_status()
    consecutive_failures = 0
    failure_alert_sent = False
    recovery_alert_sent = True  # 初始状态视为正常连接
    cookie_alert_sent = False  # 记录是否已发送cookie过期提醒

    while True:
        try:
            new_data, current_symbols, cookie_expired = fetch_positions()

            # ==================== Cookie过期检测逻辑 ====================
            if cookie_expired and not cookie_alert_sent:
                send_email(
                    "⚠️ 重要提醒: Cookie已过期",
                    f"""<h2>请立即更新Cookie!</h2>
                    <p>检测时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    <p style='color:red'><strong>系统检测到您的Binance Cookie已过期，无法继续获取数据。</strong></p>
                    <p>请按照以下步骤更新Cookie:</p>
                    <ol>
                        <li>登录您的Binance账户</li>
                        <li>使用浏览器开发者工具获取新的Cookie</li>
                        <li>更新脚本中的Cookie值</li>
                        <li>重启脚本</li>
                    </ol>
                    <p>系统将自动终止运行，请更新Cookie后才重新启动。</p>
                    """
                )
                cookie_alert_sent = True
                print("已发送Cookie过期提醒邮件，程序将退出", flush=True)
                break  # 退出监控循环，结束程序

            # ==================== 网络状态检测逻辑 ====================
            if new_data is None:
                consecutive_failures += 1
                # 连续3次失败且未提醒过
                if consecutive_failures >= 3 and not failure_alert_sent:
                    send_email(
                        "⚠️ 数据获取连续失败",
                        f"<h2>连续{consecutive_failures}次获取数据失败！</h2>"
                        f"<p>最后尝试时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>"
                        "<p style='color:red'>注意：此时仓位数据可能不准确！</p>"
                    )
                    failure_alert_sent = True
                    recovery_alert_sent = False
                time.sleep(refresh_interval)
                continue

            # 恢复检测（当从失败状态恢复时）
            if failure_alert_sent and not recovery_alert_sent:
                send_email(
                    "✅ 数据连接恢复",
                    f"<h2>数据获取已恢复正常</h2>"
                    f"<p>恢复时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>"
                    f"<p>连续失败次数：{consecutive_failures}</p>"
                )
                failure_alert_sent = False
                recovery_alert_sent = True

            consecutive_failures = 0  # 成功时重置计数器
            # ==================== 核心业务逻辑 ====================

            # 开始处理数据

            # 保留旧数据用于比较
            global last_position_data
            # 使用当前的last_position_data来检测移除的仓位
            # 不要创建新的current_last_data变量

            # 清理已不存在的交易对的状态记录
            for symbol in list(alert_status.keys()):
                if symbol not in current_symbols:
                    del alert_status[symbol]

            # 检测移除的仓位（使用最后一次有效数据对比）
            removed_symbols = set(last_position_data.keys()) - current_symbols
            for symbol in removed_symbols:
                last_data = last_position_data.get(symbol, {})
                update_position_log(symbol, "close", last_data)

                # 获取交易相关数据
                leverage = last_data.get("杠杆", "N/A")
                position_size = last_data.get("数量", "N/A")
                opening_amount = last_data.get("开仓金额", "N/A")
                position_value = last_data.get("持仓价值", "N/A")
                pnl = last_data.get("收益额", "N/A")
                roe = last_data.get("收益率", "N/A")

                # 获取仓位类型
                position_type = last_data.get("仓位类型", "多仓")

                # 移除OKX平仓操作

                send_email(
                    f"交易对 {symbol} ({position_type}) 已平仓",
                    f"""<h2>{symbol} ({position_type}) 已平仓</h2>
                    <p>检测时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    <p><strong>交易摘要:</strong></p>
                    <ul>
                        <li>仓位类型: {position_type}</li>
                        <li>杠杆: {leverage}x</li>
                        <li>持仓数量: {position_size}</li>
                        <li>开仓金额: {opening_amount} USDT</li>
                        <li>最终持仓价值: {position_value} USDT</li>
                        <li>收益额: {pnl} USDT</li>
                        <li>收益率: {roe}%</li>
                    </ul>

                    <h3>最后有效数据：</h3>
                    <table border="1">
                        {"".join(f"<tr><td>{k}</td><td>{v}</td></tr>" for k, v in last_data.items())}
                    </table>"""
                )

            # 更新数据存储
            position_data.clear()
            position_data.update(new_data)

            # 处理每个交易对
            for symbol, data in new_data.items():
                check_position_changes(symbol, data)
                check_roe_alert(symbol, data["收益率"])

            # 更新历史数据用于下次比较
            # 直接使用当前的position_data作为下一次循环的last_position_data
            last_position_data = position_data.copy()
            save_position_data()
            save_alert_status()

            time.sleep(refresh_interval)

        except Exception as e:
            logger.error(f"监控循环出错: {e}")
            consecutive_failures += 1
            time.sleep(refresh_interval)
        except KeyboardInterrupt:
            logger.info("监控已停止")
            break

    logger.info("程序已终止运行")


if __name__ == "__main__":
    monitor()